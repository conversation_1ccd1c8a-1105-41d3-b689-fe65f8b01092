<?php

declare(strict_types=1);

use App\Enums\OrderItemStatus;
use App\Models\Clinic;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Product;
use App\Models\ProductOffer;
use App\Models\Vendor;
use App\Modules\Integration\Enums\IntegrationConnectionStatus;
use App\Modules\Integration\Models\IntegrationConnection;
use App\Modules\Order\Data\OrderItemData;

beforeEach(function () {
    $this->vendor = Vendor::factory()->create();
    $this->product = Product::factory()->create();
    $this->order = Order::factory()->create();
});

test('OrderItemData includes offers array when product has active offers', function () {
    // Create a clinic and connect it to the vendor
    $clinic = Clinic::factory()->create();
    $this->order->update(['clinic_id' => $clinic->id]);

    // Create vendor connection
    IntegrationConnection::create([
        'clinic_id' => $clinic->id,
        'vendor_id' => $this->vendor->id,
        'status' => IntegrationConnectionStatus::Connected,
        'credentials' => ['username' => 'test', 'password' => 'test'],
    ]);

    // Create an active product offer
    $productOffer = ProductOffer::factory()->create([
        'product_id' => $this->product->id,
        'vendor_id' => $this->vendor->id,
        'deactivated_at' => null,
        'unit_of_measure' => 'bottle',
    ]);

    $orderItem = OrderItem::factory()->create([
        'order_id' => $this->order->id,
        'product_offer_id' => $productOffer->id,
        'status' => OrderItemStatus::Pending,
    ]);

    $orderItemData = OrderItemData::fromModel($orderItem);

    expect($orderItemData->product->offers)->toBeArray()
        ->and($orderItemData->product->offers)->toHaveCount(1)
        ->and($orderItemData->product->offers[0])->toHaveKeys(['id', 'unit_of_measure'])
        ->and($orderItemData->product->offers[0]['id'])->toBe($productOffer->id)
        ->and($orderItemData->product->offers[0]['unit_of_measure'])->toBe('bottle');
});

test('OrderItemData includes empty offers array when product has no active offers', function () {
    // Create a clinic and connect it to the vendor
    $clinic = Clinic::factory()->create();
    $this->order->update(['clinic_id' => $clinic->id]);

    // Create vendor connection
    IntegrationConnection::create([
        'clinic_id' => $clinic->id,
        'vendor_id' => $this->vendor->id,
        'status' => IntegrationConnectionStatus::Connected,
        'credentials' => ['username' => 'test', 'password' => 'test'],
    ]);

    // Create a deactivated product offer
    $productOffer = ProductOffer::factory()->create([
        'product_id' => $this->product->id,
        'vendor_id' => $this->vendor->id,
        'deactivated_at' => now(),
    ]);

    $orderItem = OrderItem::factory()->create([
        'order_id' => $this->order->id,
        'product_offer_id' => $productOffer->id,
        'status' => OrderItemStatus::Pending,
    ]);

    $orderItemData = OrderItemData::fromModel($orderItem);

    expect($orderItemData->product->offers)->toBeArray()
        ->and($orderItemData->product->offers)->toBeEmpty();
});

test('OrderItemData includes empty offers array when product has no offers at all', function () {
    // Create a clinic
    $clinic = Clinic::factory()->create();
    $this->order->update(['clinic_id' => $clinic->id]);

    // Create a product with no offers
    $productWithNoOffers = Product::factory()->create();

    $orderItem = OrderItem::factory()->create([
        'order_id' => $this->order->id,
        'product_offer_id' => ProductOffer::factory()->create()->id,
        'status' => OrderItemStatus::Pending,
    ]);

    // Manually set the product relationship to our product with no offers
    $orderItem->setRelation('product', $productWithNoOffers);

    $orderItemData = OrderItemData::fromModel($orderItem);

    expect($orderItemData->product->offers)->toBeArray()
        ->and($orderItemData->product->offers)->toBeEmpty();
});

test('OrderItemData includes multiple offers when product has multiple active offers', function () {
    $clinic = Clinic::factory()->create();
    $this->order->update(['clinic_id' => $clinic->id]);

    // Create vendor connection
    IntegrationConnection::create([
        'clinic_id' => $clinic->id,
        'vendor_id' => $this->vendor->id,
        'status' => IntegrationConnectionStatus::Connected,
        'credentials' => ['username' => 'test', 'password' => 'test'],
    ]);

    // Create multiple active product offers
    $productOffer1 = ProductOffer::factory()->create([
        'product_id' => $this->product->id,
        'vendor_id' => $this->vendor->id,
        'deactivated_at' => null,
        'unit_of_measure' => 'bottle',
    ]);

    $productOffer2 = ProductOffer::factory()->create([
        'product_id' => $this->product->id,
        'vendor_id' => $this->vendor->id,
        'deactivated_at' => null,
        'unit_of_measure' => 'box',
    ]);

    $orderItem = OrderItem::factory()->create([
        'order_id' => $this->order->id,
        'product_offer_id' => $productOffer1->id,
        'status' => OrderItemStatus::Pending,
    ]);

    $orderItemData = OrderItemData::fromModel($orderItem);

    expect($orderItemData->product->offers)->toBeArray()
        ->and($orderItemData->product->offers)->toHaveCount(2)
        ->and($orderItemData->product->offers[0])->toHaveKeys(['id', 'unit_of_measure'])
        ->and($orderItemData->product->offers[1])->toHaveKeys(['id', 'unit_of_measure']);
});

test('OrderItemData offers array contains correct structure and data', function () {
    $clinic = Clinic::factory()->create();
    $this->order->update(['clinic_id' => $clinic->id]);

    // Create vendor connection
    IntegrationConnection::create([
        'clinic_id' => $clinic->id,
        'vendor_id' => $this->vendor->id,
        'status' => IntegrationConnectionStatus::Connected,
        'credentials' => ['username' => 'test', 'password' => 'test'],
    ]);

    // Create an active product offer with specific unit of measure
    $productOffer = ProductOffer::factory()->create([
        'product_id' => $this->product->id,
        'vendor_id' => $this->vendor->id,
        'deactivated_at' => null,
        'unit_of_measure' => 'tablet',
    ]);

    $orderItem = OrderItem::factory()->create([
        'order_id' => $this->order->id,
        'product_offer_id' => $productOffer->id,
        'status' => OrderItemStatus::Pending,
    ]);

    $orderItemData = OrderItemData::fromModel($orderItem);

    expect($orderItemData->product->offers)->toBeArray()
        ->and($orderItemData->product->offers)->toHaveCount(1)
        ->and($orderItemData->product->offers[0])->toMatchArray([
            'id' => $productOffer->id,
            'unit_of_measure' => 'tablet',
        ]);
});
