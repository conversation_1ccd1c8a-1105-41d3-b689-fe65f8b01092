import { fetchApi } from '@/libs/utils/api';
import dayjs from 'dayjs';

export const exportCsv = async (
  path: string,
  filters: Record<string, string>,
) => {
  try {
    const response = await fetchApi(
      `${path}?${new URLSearchParams(filters).toString()}`,
      {
        method: 'GET',
        authStrategy: 'token',
        options: {
          headers: {
            Accept: 'text/csv',
          },
        },
      },
    );

    const csvContent =
      typeof response === 'string' ? response : JSON.stringify(response);
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `spend-analysis-${dayjs().format('YYYY-MM-DD')}.csv`;
    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);
  } catch (error) {
    console.error('Export failed:', error);
  }
};
