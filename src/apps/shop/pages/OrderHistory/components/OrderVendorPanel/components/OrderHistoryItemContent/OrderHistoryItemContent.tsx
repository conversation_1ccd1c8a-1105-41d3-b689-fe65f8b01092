import { Link } from 'react-router-dom';
import { getProductUrl } from '@/apps/shop/routes/utils';
import { getPriceString } from '@/utils';
import type { OrderHistoryDetailItemType } from '@/libs/orders/types';
import { Tooltip } from '@/libs/ui/Tooltip';
import { t } from 'i18next';

interface OrderHistoryItemContentProps {
  item: OrderHistoryDetailItemType;
}

export const OrderHistoryItemContent = ({
  item,
}: OrderHistoryItemContentProps) => {
  return (
    <div className="ml-4 flex h-full flex-col justify-between">
      <div>
        <p className="mb-0.5 text-xs font-medium text-gray-600/70">
          Order ID: {item.orderNumber}
        </p>
        {item.product.hasOffers ? (
          <Link
            to={getProductUrl(item.product.id, item.productOfferId)}
            className="cursor-pointer font-medium text-gray-800 no-underline hover:underline"
          >
            {item.product.name}
          </Link>
        ) : (
          <Tooltip label={t('client.orderHistoryItem.notAvailable')}>
            <span
              tabIndex={0}
              aria-disabled="true"
              className="font-medium text-gray-800"
            >
              {item.product.name}
            </span>
          </Tooltip>
        )}
      </div>
      <div className="mt-2 flex items-center text-sm">
        <p className="min-w-[6rem] py-3 pr-3 text-gray-600">
          Quantity:{' '}
          <span className="font-bold text-gray-800">{item.quantity}</span>
        </p>
        <div
          className="mx-4 w-px self-stretch bg-gray-200"
          aria-hidden="true"
        />
        <p className="p-3 text-gray-600">
          Price:{' '}
          <span className="font-bold text-gray-800">
            {getPriceString(item.unitPrice)}
          </span>
        </p>
        <div
          className="mx-4 w-px self-stretch bg-gray-200"
          aria-hidden="true"
        />
        <p className="p-3 text-gray-600">
          Net Total:{' '}
          {item.totalPrice ? (
            <span className="font-bold text-gray-800">
              {getPriceString(item.totalPrice)}
            </span>
          ) : (
            <span className="font-bold text-green-700">Free</span>
          )}
        </p>
      </div>
    </div>
  );
};
