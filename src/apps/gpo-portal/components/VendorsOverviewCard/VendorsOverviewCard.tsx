import { useEffect } from 'react';
import { VendorCard } from './components/VendorCard/VendorCard';
import { useTimePeriod } from '@/libs/utils/hooks/useTimePeriod/useTimePeriod';
import { Select } from '@/libs/form/Select';
import type { PeriodOption } from '@/libs/utils/hooks/useTimePeriod/useTimePeriod';
import { CollapsiblePanel } from '@/libs/ui/CollapsiblePanel/CollapsiblePanel';
import { Button } from '@/libs/ui/Button/Button';
import { Icon } from '@/libs/icons/Icon';
import { useVendorsOverview } from './hooks/useVendorsOverview';

export const VendorsOverviewCard = () => {
  const {
    options: timePeriodsOptions,
    period: selectedPeriod,
    setPeriod,
    formattedRange,
    startDate,
    endDate,
  } = useTimePeriod({
    defaultPeriod: 'entire year',
    availableOptions: ['entire year', 'Q1', 'Q2', 'Q3', 'Q4'],
  });

  const { data, isLoading, error, updateFilters } = useVendorsOverview({
    date_from: startDate,
    date_to: endDate,
  });

  const { total_spend: totalSpend, vendors } = data;

  useEffect(() => {
    updateFilters({ date_from: startDate, date_to: endDate });
  }, [startDate, endDate, updateFilters]);

  const timePeriods = timePeriodsOptions.map((option) => ({
    value: option,
    label:
      option.charAt(0).toUpperCase() +
      option.slice(1).replace(/([A-Z])/g, ' $1'),
  }));

  return (
    <CollapsiblePanel
      startOpen
      header={
        <div className="flex w-full items-center justify-between py-3 pr-18 pl-6">
          <h3 className="text-base font-medium text-[#344054]">
            Vendors Overview
          </h3>

          <div className="w-[160px]">
            <Select
              value={selectedPeriod}
              onChange={(e) => setPeriod(e.target.value as PeriodOption)}
              options={timePeriods}
              showEmptyOption={false}
            />
          </div>
        </div>
      }
      content={
        <div className="bg-white p-6">
          <div className="flex flex-col gap-4 rounded bg-[#FAFAFA] p-6">
            {vendors && vendors.length > 0 ? (
              <div className="flex items-center justify-between gap-2">
                <p className="text-sm text-[#666]">
                  Results filtered by:{' '}
                  <span className="font-bold text-[#333] capitalize">
                    {selectedPeriod} ({formattedRange})
                  </span>
                </p>
                <div className="flex items-center gap-2">
                  <Button
                    variant="white"
                    className="max-w-[60px]"
                    aria-label="Download"
                    onClick={() => {
                      console.log('WIP...');
                    }}
                  >
                    <Icon name="download" aria-hidden={true} />
                  </Button>
                  <Button
                    variant="unstyled"
                    className="max-w-[60px]"
                    aria-label="More options"
                    onClick={() => {
                      console.log('WIP...');
                    }}
                  >
                    <Icon name="moreOptions" aria-hidden={true} />
                  </Button>
                </div>
              </div>
            ) : null}

            {isLoading ? (
              <div className="flex flex-col gap-4">
                {[1, 2, 3].map((index) => (
                  <div
                    key={index}
                    className="h-32 animate-pulse rounded-xl border border-[#F5F5F5] bg-gray-100 p-5"
                  />
                ))}
              </div>
            ) : error ? (
              <div className="flex items-center justify-center py-8">
                <p className="text-sm text-red-600">
                  Failed to load vendors data
                </p>
              </div>
            ) : vendors && vendors.length > 0 ? (
              vendors.map((vendorData) => (
                <VendorCard
                  key={vendorData.id}
                  vendorData={vendorData}
                  totalSpend={totalSpend}
                />
              ))
            ) : (
              <div className="flex items-center justify-center py-8">
                <p className="text-sm text-gray-500">No vendors found</p>
              </div>
            )}
          </div>
        </div>
      }
    />
  );
};
