import { useState, useEffect, useCallback } from 'react';
import {
  AggregatedSpendAnalysisResponse,
  SpendAnalysisFilters,
} from '../types';
import { fetchApi } from '@/libs/utils/api';

export const useSpendAnalysisSummary = (
  initialFilters: SpendAnalysisFilters = {},
) => {
  const [data, setData] = useState<AggregatedSpendAnalysisResponse | null>(
    null,
  );
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<SpendAnalysisFilters>({
    date_from: undefined,
    date_to: undefined,
    ...initialFilters,
  });

  const fetchData = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      const queryParams = new URLSearchParams();
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryParams.append(key, value.toString());
        }
      });

      const url = `/gpo/spend-analysis/summary${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
      const response = await fetchApi<AggregatedSpendAnalysisResponse>(url, {
        method: 'GET',
        authStrategy: 'token',
      });

      setData(response);
    } catch (err) {
      const errorMessage =
        err instanceof Error
          ? err.message
          : 'Failed to fetch spend analysis summary data';
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, [filters]);

  const updateFilters = useCallback(
    (newFilters: Partial<SpendAnalysisFilters>) => {
      setFilters((prev) => ({
        ...prev,
        ...newFilters,
      }));
    },
    [],
  );

  const refresh = useCallback(() => {
    fetchData();
  }, [fetchData]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  return {
    data,
    isLoading,
    error,
    filters,
    updateFilters,
    refresh,
  };
};
