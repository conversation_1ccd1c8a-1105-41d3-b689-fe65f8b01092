export interface AggregatedSpendAnalysisResponse {
  non_preffered_percent: number;
  non_preffered_spend: number;
  preferred_percent: number;
  preferred_spend: number;
  total_spend: number;
}

export interface SpendOverview {
  total_spend: number;
  preferred_spend: number;
  non_preferred_spend: number;
  preferred_percentage: number;
  non_preferred_percentage: number;
  average_spend_per_clinic: number;
  total_rebates_earned: number;
  total_clinics: number;
  active_clinics: number;
  inactive_clinics: number;
}

export interface TopClinic {
  id: string;
  name: string;
  total_spend: number;
  preferred_spend: number;
  preferred_percentage: number;
  status: 'active' | 'inactive';
  member_since: string;
}

export interface VendorDistribution {
  vendor_id: string;
  vendor_name: string;
  total_spend: number;
  clinic_count: number;
  preferred_clinics: number;
  category: string;
}

export interface CategoryBreakdown {
  category: string;
  total_spend: number;
  clinic_count: number;
  preferred_percentage: number;
  top_vendors: string[];
}

export interface SpendTrend {
  period: string;
  total_spend: number;
  preferred_spend: number;
  preferred_percentage: number;
  clinic_count: number;
}

export interface SpendAnalysisFilters {
  date_from?: string;
  date_to?: string;
  clinic_status?: 'all' | 'active' | 'inactive';
  vendor_category?: string;
  min_spend?: number;
  max_spend?: number;
}
