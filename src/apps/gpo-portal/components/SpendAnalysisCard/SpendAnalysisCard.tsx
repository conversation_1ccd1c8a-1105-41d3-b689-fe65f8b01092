import React from 'react';
import { DashboardCard } from '@/libs/dashboard/DashboardCard/DashboardCard';
import { DashboardCardLoader } from '@/libs/dashboard/DashboardCardLoader/DashboardCardLoader';
import { GPO_ROUTES_PATH } from '../../routes/routes';
import { useNavigate } from 'react-router-dom';
import { useSpendAnalysisSummary } from './hooks/useSpendAnalysisSummary';
import { DashboardCardError } from '@/libs/dashboard/DashboardCardError/DashboardCardError';
import { exportCsv } from '@/libs/dashboard/utils/exportCsv';

interface SpendAnalysisCardProps {
  className?: string;
}

export const SpendAnalysisCard: React.FC<SpendAnalysisCardProps> = ({
  className,
}) => {
  const { data, isLoading, error } = useSpendAnalysisSummary();
  const navigate = useNavigate();

  const handleCtaClick = () => {
    navigate(GPO_ROUTES_PATH.spendAnalysis);
  };

  if (isLoading) {
    return <DashboardCardLoader title="Spend Analysis" className={className} />;
  }

  if (error || !data) {
    return <DashboardCardError className={className} title="Spend Analysis" />;
  }

  return (
    <DashboardCard
      className={className}
      title="Spend Analysis"
      percentage={data.preferred_percent}
      percentageLabel="SPEND ON PREFERRED VENDORS"
      metrics={[
        {
          label: 'Total',
          value: `$${data.total_spend.toLocaleString()}`,
        },
        {
          label: 'Preferred Vendors',
          value: `$${data.preferred_spend.toLocaleString()}`,
        },
      ]}
      progressData={{
        activePercentage: data.preferred_percent,
        activeLabel: 'Preferred',
        activeValue: `$${data.preferred_spend.toLocaleString()}`,
        inactivePercentage: data.non_preffered_percent,
        inactiveLabel: 'Non-Preferred',
        inactiveValue: `$${data.non_preffered_spend.toLocaleString()}`,
      }}
      ctaText="Detailed Spend Report"
      onCtaClick={handleCtaClick}
      onDownloadClick={() => exportCsv('/gpo/spend-analysis/export', {})}
      dateRangeOptions={{
        defaultTimePeriod: 'entire year',
        timePeriodOptions: ['entire year', 'Q1', 'Q2', 'Q3', 'Q4'],
        onUpdateTimePeriod: () => {
          // TODO: Implement time period update
        },
      }}
    />
  );
};
